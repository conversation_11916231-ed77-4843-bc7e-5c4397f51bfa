import { ContentListUnion, GoogleGenAI } from "@google/genai";
import Config from "../Config";
import { ResponseCacheEnabled, ResponseHash, ResponseWithUsage, StreamedResponseChunk } from "./LlmCommon";
import { cachePromise, hash } from "../Cache";
import { MetricData } from "../cost-metrics";
import { toGeminiSchema } from "gemini-zod";
import { z } from "zod";
import logger from "../logger";

/**
 * Constants for Gemini model names
 */
export const GEMINI_PRO_MODEL = "gemini-2.5-pro-preview-03-25";
export const GEMINI_FLASH_MODEL = "gemini-2.0-flash";

/**
 * Common parameters for Gemini API calls
 */
type GeminiCompletionParams = {
	/** The model to use for completion */
	model: string;
	/** The content to send to the model */
	contents: ContentListUnion;
	/** Optional system instruction for the model */
	systemInstruction?: string;
	/** Cache index for response caching. Set to undefined to disable caching */
	cacheIdx?: number;
	/** Metric data for tracking usage */
	metricData: MetricData;
};

const genAI = new GoogleGenAI({ apiKey: Config.GEMINI_API_KEY });

/**
 * Get the usage object for Gemini API based on the model and token counts
 * Calculates costs according to the pricing from: https://ai.google.dev/gemini-api/docs/pricing
 *
 * @param params - Parameters containing model, metadata, and metrics information
 * @returns A usage object with token counts and calculated costs
 */
function getGeminiUsage(params: {
	model: string;
	metricData: MetricData;
	usageMetadata: { promptTokenCount?: number; candidatesTokenCount?: number; cachedContentTokenCount?: number };
}) {
	// Extract token counts from metadata
	const inputTokens = params.usageMetadata.promptTokenCount ?? 0;
	const outputTokens = params.usageMetadata.candidatesTokenCount ?? 0;
	const cachedTokens = params.usageMetadata.cachedContentTokenCount ?? 0;

	const { model, metricData } = params;
	const nonCachedInputTokens = inputTokens - cachedTokens;

	// Calculate costs based on model
	let inputCost = 0;
	let outputCost = 0;

	// Check if prompt exceeds 200k tokens
	const isLargePrompt = inputTokens > 200000;

	switch (model) {
		case GEMINI_PRO_MODEL: // Gemini 2.5 Pro Preview
			// Input: $1.25 per million tokens (or $2.50 if large prompt)
			// Output: $10.00 per million tokens (or $15.00 if large prompt)
			if (isLargePrompt) {
				inputCost = (nonCachedInputTokens * 2.5) / 1_000_000;
				outputCost = (outputTokens * 15.0) / 1_000_000;
			} else {
				inputCost = (nonCachedInputTokens * 1.25) / 1_000_000;
				outputCost = (outputTokens * 10.0) / 1_000_000;
			}
			break;

		case GEMINI_FLASH_MODEL: // Gemini 2.0 Flash
			// Input: $0.10 per million tokens
			// Output: $0.40 per million tokens
			// Cached: $0.025 per million tokens
			inputCost = (nonCachedInputTokens * 0.1) / 1_000_000;
			outputCost = (outputTokens * 0.4) / 1_000_000;

			// Add cached tokens cost
			inputCost += (cachedTokens * 0.025) / 1_000_000;
			break;

		default:
			logger.warn(`Unknown Gemini model: ${model}, using default pricing (${GEMINI_PRO_MODEL})`);
			// Default to Gemini 2.5 Pro Preview pricing
			inputCost = (nonCachedInputTokens * 1.25) / 1_000_000;
			outputCost = (outputTokens * 10.0) / 1_000_000;
	}

	return {
		type: "usage",
		inputTokens,
		outputTokens,
		inputCost,
		outputCost,
		cachedTokens,
		metricData,
	} as const;
}

/**
 * Native version of textCompletionGemini that accepts native Gemini API contents
 * instead of SimpleChatMessage[]
 *
 * @param params - Parameters for the text completion
 * @returns A promise that resolves to the response with usage information
 */
export async function textCompletionGemini(params: GeminiCompletionParams): Promise<ResponseWithUsage<string>> {
	const llmParams = {
		model: params.model,
		contents: params.contents,
		config: {
			systemInstruction: params.systemInstruction,
		},
	};

	const cache = await cachePromise;

	const hashKey =
		ResponseCacheEnabled && params.cacheIdx !== undefined ? `gemini-native-text-${params.cacheIdx}-${hash(llmParams)}` : undefined;
	if (hashKey) {
		const cachedResponse = await cache.get<ResponseWithUsage<string>>(hashKey);

		if (cachedResponse) {
			return cachedResponse;
		}
	}

	const MaxAttempts = 3;
	let attemptsCount = 0;
	let response;
	let lastError: Error | undefined;

	// Retry loop for when response.text is undefined
	while (attemptsCount < MaxAttempts) {
		attemptsCount++;

		try {
			response = await genAI.models.generateContent(llmParams);

			// Check if response has text content
			if (response.text) {
				break; // Success, exit retry loop
			} else {
				lastError = new Error(`No text found in response (attempt ${attemptsCount}/${MaxAttempts})`);
				logger.warn(`Gemini API returned undefined text on attempt ${attemptsCount}/${MaxAttempts}`);

				// Add a small delay between retries (exponential backoff)
				if (attemptsCount < MaxAttempts) {
					const delayMs = Math.min(1000 * Math.pow(2, attemptsCount - 1), 5000); // 1s, 2s, 4s max
					await new Promise((resolve) => setTimeout(resolve, delayMs));
				}
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));
			logger.warn(`Gemini API call failed on attempt ${attemptsCount}/${MaxAttempts}:`, error);

			// Add a small delay between retries for API errors too
			if (attemptsCount < MaxAttempts) {
				const delayMs = Math.min(1000 * Math.pow(2, attemptsCount - 1), 5000);
				await new Promise((resolve) => setTimeout(resolve, delayMs));
			}
		}
	}

	// If we exhausted all attempts without success, throw error
	if (!response || !response.text) {
		console.error("Final Gemini response:", JSON.stringify(response, null, 2));
		throw new Error(
			`Failed to get text response from Gemini after ${MaxAttempts} attempts. Last error: ${lastError?.message || "Unknown error"}`,
		);
	}

	// Extract usage information if available
	let usage;
	if (response.usageMetadata) {
		usage = getGeminiUsage({
			model: params.model,
			usageMetadata: response.usageMetadata,
			metricData: params.metricData,
		});
	}

	const result = {
		response: response.text!,
		usage,
	};

	if (hashKey) {
		await cache.set(hashKey, result, Config.COMPLETION_CACHE_TTL!);
	}

	return result;
}

/**
 * Native version of streamCompletionGemini that accepts native Gemini API contents
 * instead of SimpleChatMessage[]
 *
 * @param params - Parameters for the streaming text completion
 * @returns An async generator that yields response chunks
 */
export async function* streamCompletionGemini(params: GeminiCompletionParams): AsyncGenerator<StreamedResponseChunk> {
	const llmParams = {
		model: params.model,
		contents: params.contents,
		config: {
			systemInstruction: params.systemInstruction,
		},
	};

	const hashKey =
		ResponseCacheEnabled && params.cacheIdx !== undefined ? `gemini-native-stream-${params.cacheIdx}-${hash(llmParams)}` : undefined;

	const cache = await cachePromise;
	const cachedResponse = hashKey ? await cache.get<StreamedResponseChunk[]>(hashKey) : undefined;
	if (cachedResponse) {
		for (const chunk of cachedResponse) {
			yield chunk;
		}
	} else {
		const stream = await genAI.models.generateContentStream(llmParams);

		const valueToCache: StreamedResponseChunk[] = [];

		for await (const chunk of stream) {
			if (chunk.text) {
				valueToCache.push(chunk.text);
				yield chunk.text;
			} else if (chunk.usageMetadata) {
				// Get usage object with costs based on the model and token counts
				const usage = getGeminiUsage({
					model: params.model,
					usageMetadata: chunk.usageMetadata,
					metricData: params.metricData,
				});
				valueToCache.push(usage);
				yield usage;
			}
		}

		const responseHash: ResponseHash = {
			type: "responseHash",
			hash: hash(valueToCache),
		} as const;

		yield responseHash;

		valueToCache.push(responseHash);

		if (hashKey) {
			await cache.set(hashKey, valueToCache, Config.COMPLETION_CACHE_TTL!);
		}
	}
}

/**
 * Structured completion with Gemini API that returns a typed response based on a Zod schema
 *
 * @param params - Parameters for the structured completion
 * @returns A promise that resolves to the typed response with usage information
 */
export async function structuredCompletionGemini<ZodInput extends z.ZodType, R = z.infer<ZodInput>>(
	params: GeminiCompletionParams & {
		/** Schema defining the structure of the expected response */
		responseSchema: ZodInput;
	},
): Promise<ResponseWithUsage<R>> {
	const geminiSchema = toGeminiSchema(params.responseSchema);

	const llmParams = {
		model: params.model,
		contents: params.contents,
		config: {
			systemInstruction: params.systemInstruction,
			responseMimeType: "application/json",
			responseSchema: geminiSchema,
		},
	};

	const cache = await cachePromise;

	const hashKey =
		ResponseCacheEnabled && params.cacheIdx !== undefined ? `gemini-structured-${params.cacheIdx}-${hash(llmParams)}` : undefined;
	if (hashKey) {
		const cachedResponse = await cache.get<ResponseWithUsage<R>>(hashKey);

		if (cachedResponse) {
			return cachedResponse;
		}
	}

	const MaxAttempts = 3;
	let attemptsCount = 0;
	let response;
	let lastError: Error | undefined;

	// Retry loop for when response.text is undefined
	while (attemptsCount < MaxAttempts) {
		attemptsCount++;

		try {
			response = await genAI.models.generateContent(llmParams);

			// Check if response has text content
			if (response.text) {
				break; // Success, exit retry loop
			} else {
				lastError = new Error(`No text found in structured response (attempt ${attemptsCount}/${MaxAttempts})`);
				logger.warn(`Gemini API returned undefined text for structured completion on attempt ${attemptsCount}/${MaxAttempts}`, {
					model: params.model,
					responseKeys: Object.keys(response),
					usageMetadata: response.usageMetadata,
				});

				// Add a small delay between retries (exponential backoff)
				if (attemptsCount < MaxAttempts) {
					const delayMs = Math.min(1000 * Math.pow(2, attemptsCount - 1), 5000); // 1s, 2s, 4s max
					await new Promise((resolve) => setTimeout(resolve, delayMs));
				}
			}
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));
			logger.warn(`Gemini structured API call failed on attempt ${attemptsCount}/${MaxAttempts}:`, error);

			// Add a small delay between retries for API errors too
			if (attemptsCount < MaxAttempts) {
				const delayMs = Math.min(1000 * Math.pow(2, attemptsCount - 1), 5000);
				await new Promise((resolve) => setTimeout(resolve, delayMs));
			}
		}
	}

	// If we exhausted all attempts without success, throw error
	if (!response || !response.text) {
		console.error("Final Gemini structured response:", JSON.stringify(response, null, 2));
		throw new Error(
			`Failed to get text response from Gemini structured completion after ${MaxAttempts} attempts. Last error: ${lastError?.message || "Unknown error"}`,
		);
	}

	// Extract usage information if available
	const usage = getGeminiUsage({
		model: params.model,
		usageMetadata: response.usageMetadata!,
		metricData: params.metricData,
	});

	const result = {
		response: JSON.parse(response.text!) as R,
		usage,
	};

	if (hashKey) {
		await cache.set(hashKey, result, Config.COMPLETION_CACHE_TTL!);
	}

	return result;
}
