import { EfficiencyReportData } from "./EfficiencyReportData";

function getMonth(data: EfficiencyReportData, from: string, to: string): string | null {
	const fromDate = new Date(from);
	const toDate = new Date(to);

	const isFullMonth = (date: Date): boolean => {
		const startOfMonth = new Date(fromDate.getFullYear(), date.getMonth(), 1);
		const endOfMonth = new Date(toDate.getFullYear(), date.getMonth() + 1, 0);
		return fromDate.getDate() === startOfMonth.getDate() && toDate.getDate() === endOfMonth.getDate();
	};

	if (fromDate.getFullYear() === toDate.getFullYear() && fromDate.getMonth() === toDate.getMonth() && isFullMonth(fromDate)) {
		return `${data.monthNames[fromDate.getMonth()]} ${fromDate.getFullYear()}`;
	}

	return null;
}

export type PeriodNames = {
	periodNames: { [p: number]: string };
	periodsMatchMonths: boolean;
};

export function getPeriodNames(data: EfficiencyReportData): PeriodNames {
	// The last period is summary period, so we ignore it
	return data.periods.slice(0, -1).reduce(
		(acc, period, idx) => {
			const month = getMonth(data, period.from, period.to);

			acc.periodNames[idx] = month ? month : `Period ${idx + 1}`;
			acc.periodsMatchMonths = month != null && acc.periodsMatchMonths;

			return acc;
		},
		{
			periodNames: {} as { [key: number]: string },
			periodsMatchMonths: true as boolean,
		},
	);
}
