/** @jsxImportSource @emotion/react */
import type { AllFeedback } from 'pigbot-backend/src/feedback/FeedbackService';
import React, { useEffect, useMemo } from 'react';
import { Section, splitMarkdownV2, splitMarkdownV3 } from 'pigbot-core/src/feedback/splitMarkdown';
import { ENTIRE_RESPONSE_KEY, GOOD_RESPONSE } from 'pigbot-core/src/Constants';
import Alert from 'antd/lib/alert';
import { css } from '@emotion/react';
import PigbotMarkdown from '@src/components/common/PigbotMarkdown';
import { AnalysisComponent } from '@src/components/report_assistant/AnalysisComponent';
import { AnalysisOpenedStates } from '@src/components/report_assistant/ReportAssistant';

type Props = {
	responseMD: string;
	feedback: AllFeedback;
	analysis: string[];
	version: string;
	analysisOpenedStates: AnalysisOpenedStates;
};

function sectionHasFeedback(section: Section, sectionId: string, feedback: AllFeedback): boolean {
	return (
		feedback[sectionId] !== undefined ||
		section.children.map((ch, index) => sectionHasFeedback(ch, `${sectionId}-${index}`, feedback)).some((v) => v)
	);
}

function sectionsHasFeedback(sections: Section[], sectionId: string, feedback: AllFeedback): boolean {
	return sections.map((s, index) => sectionHasFeedback(s, `${sectionId}-${index}`, feedback)).some((v) => v);
}

export const ResponseWithFeedback: React.FC<Props> = ({ responseMD, feedback, analysis, version, analysisOpenedStates }) => {
	const rootSections = useMemo(() => {
		if (version === 'vfa-v3') return splitMarkdownV3(responseMD, true);
		else return splitMarkdownV2(responseMD);
	}, [responseMD]);

	const analysisSections = useMemo(() => analysis.map((a) => splitMarkdownV3(a)), [analysis]);

	useEffect(() => {
		if (!analysisOpenedStates.isInitialized()) {
			analysisSections.map((sections, index) =>
				sectionsHasFeedback(sections, `A-${index}`, feedback)
					? analysisOpenedStates.openAnalysis(index)
					: analysisOpenedStates.closeAnalysis(index),
			);
		}
	}, [analysisOpenedStates]);

	//v1 and v2 does not contain analysis.

	function renderFeedback(key: string) {
		const currentSectionFeedback: string | undefined = feedback[key];
		const isGoodResponse = currentSectionFeedback === GOOD_RESPONSE;

		return (
			currentSectionFeedback && (
				<Alert
					type={isGoodResponse ? 'success' : 'warning'}
					message={isGoodResponse ? 'Good response.' : currentSectionFeedback}
					css={css`
						margin-left: 35px;
						margin-bottom: 10px;
						white-space: break-spaces;
					`}
				/>
			)
		);
	}

	function renderSection(section: Section, depth: number, sectionId: string) {
		return (
			<div
				key={sectionId}
				style={{
					paddingLeft: `${depth * 20}px`,
				}}
			>
				<PigbotMarkdown>{section.content}</PigbotMarkdown>
				{renderSections(section.children, depth + 1, sectionId)}
				{renderFeedback(sectionId)}
			</div>
		);
	}

	function renderSections(sections: Section[], depth: number = 0, parentKey?: string) {
		return (
			<>
				{sections.map((section, index) => {
					const sectionId = parentKey ? `${parentKey}-${index}` : `${index}`;
					return renderSection(section, depth, sectionId);
				})}
			</>
		);
	}

	return (
		<>
			{rootSections.map((section, index) => {
				return (
					<div key={index}>
						{renderSection(section, 0, `${index}`)}
						{analysisSections[index] && (
							<AnalysisComponent
								analysisComponent={renderSections(analysisSections[index], 0, `A-${index}`)}
								isOpened={() => analysisOpenedStates.isAnalysisOpen(index)}
								toggleOpened={() => analysisOpenedStates.toggleAnalysis(index)}
							/>
						)}
						{<hr />}
					</div>
				);
			})}
			{renderFeedback(ENTIRE_RESPONSE_KEY)}
		</>
	);
};
