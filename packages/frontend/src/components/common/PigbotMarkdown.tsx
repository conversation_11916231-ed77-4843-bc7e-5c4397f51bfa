/** @jsxImportSource @emotion/react */
import React from 'react';
import ReactMarkdown, { Options } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import MarkdownStyle from '@src/components/common/MarkdownStyle';
import <PERSON>hartRenderer from '@src/components/common/EchartRenderer';
import { Components } from 'react-markdown/lib';
import { convertJsonToGraphOpt } from 'pigbot-core/src/eff-report/GraphConverters';

interface PigbotMarkdownProps extends Options {}

const PigbotMarkdown: React.FC<PigbotMarkdownProps> = (props) => {
	const { children, remarkPlugins = [], rehypePlugins = [], components = {}, ...rest } = props;

	// Merge custom components with our default components
	const mergedComponents: Components = {
		// Default component for handling graph code blocks
		pre({ children, ...preProps }) {
			// Check if this pre element contains a code element with language 'graph'
			const codeChild = React.Children.toArray(children).find((child) => {
				if (!React.isValidElement(child)) return false;
				// Check if it's a code element by examining its properties
				return child.props && child.props.className && /language-\w+/.test(child.props.className);
			});

			if (codeChild && React.isValidElement(codeChild)) {
				const className = codeChild.props.className || '';
				const match = /language-(\w+)/.exec(className);
				const language = match && match[1];

				if (!language || !codeChild.props.children) {
					return `<pre><code class="${language ? `language-${language}` : ''}">${codeChild.props.children}</code></pre>`;
				}

				const { title, convertedOptions, explanation, height } = convertJsonToGraphOpt(language!, codeChild.props.children);

				if (convertedOptions && title && explanation) {
					try {
						const chartData = {
							title: title,
							option: convertedOptions,
							explanation: explanation,
							height,
						};
						return <EChartRenderer value={chartData} />;
					} catch (error) {
						// eslint-disable-next-line no-console
						console.error('Error parsing graph data:', error);
						return <div className='echart-error'>Invalid graph configuration</div>;
					}
				}
			}

			console.warn('Incorrect usage of code block in markdown', React.Children.toArray(children));
			// Hide unknown <pre> elements as they breaks page layout.
			return <></>;
		},
		// Add any other custom components here
		...components,
	};

	return (
		<ReactMarkdown
			css={MarkdownStyle}
			remarkPlugins={[remarkGfm, ...(remarkPlugins ?? [])]}
			rehypePlugins={rehypePlugins}
			components={mergedComponents}
			{...rest}
		>
			{children}
		</ReactMarkdown>
	);
};

export default PigbotMarkdown;
