/** @jsxImportSource @emotion/react */
import '../../common/reset.less';
import { css } from '@emotion/react';
import React, { useMemo, useState } from 'react';

import Button from 'antd/lib/button';
import 'antd/lib/button/style/index.less';

import Card from 'antd/lib/card';
import 'antd/lib/card/style/index.less';

import Checkbox from 'antd/lib/checkbox';
import 'antd/lib/checkbox/style/index.less';
import { EfficiencyReportData } from 'pigbot-core/src/EfficiencyReportData';
import { useWindowSize } from 'react-use';
import TrpcReact from '@src/common/TrpcReact';
import Providers, { useUserContext } from '@src/components/Providers';
import { createRoot } from 'react-dom/client';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { FeedbackMode, FeedbackState } from '@src/components/report_assistant/FeedbackMode';
import { LoadingError } from '@src/components/common/LoadingError';
import { EffReportSummaryView } from '@src/components/report_assistant/EffReportSummaryView';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import { EffReportSummaryViewerState } from '@src/components/report_assistant/EffReportSummaryViewerState';

export class AnalysisOpenedStates {
	// If the state is not initialized, and user goes to feedback page, the page will initialize the state and open only analyses with feedback. See ResponseWithFeedback.tsx
	openedAnalysis?: Set<number>;

	constructor() {
		makeAutoObservable(this);
	}

	isInitialized() {
		return this.openedAnalysis !== undefined;
	}

	initializeAnalysis() {
		this.openedAnalysis = new Set();
	}

	openAnalysis(index: number) {
		if (!this.openedAnalysis) this.initializeAnalysis();
		this.openedAnalysis!.add(index);
	}

	closeAnalysis(index: number) {
		if (!this.openedAnalysis) this.initializeAnalysis();
		this.openedAnalysis!.delete(index);
	}

	isAnalysisOpen(index: number) {
		return this.openedAnalysis?.has(index) || false;
	}

	toggleAnalysis(index: number) {
		if (this.isAnalysisOpen(index)) this.closeAnalysis(index);
		else this.openAnalysis(index);
	}
}

class State {
	isVisible: boolean = false;
	enabled: boolean = false;
	toolsVisible: boolean = false;

	viewState: EffReportSummaryViewerState = new EffReportSummaryViewerState();

	feedbackStates: { [key: string]: FeedbackState } = {};
	editingFeedback: boolean = false;

	openedAnalysis: AnalysisOpenedStates = new AnalysisOpenedStates();

	constructor() {
		makeAutoObservable(this);
	}

	editFeedback(feedback: FeedbackState) {
		if (this.feedbackStates[feedback.responseId] === undefined) this.feedbackStates[feedback.responseId] = feedback;
		this.editingFeedback = true;
	}

	stopFeedback() {
		this.editingFeedback = false;
	}

	toggleVisibility() {
		this.isVisible = !this.isVisible;
		this.enabled = this.enabled || this.isVisible;
	}

	setToolsVisible(visible: boolean) {
		this.toolsVisible = visible;
	}
}

type Props = {
	jwtToken: string;
	reportResponse: EfficiencyReportData;
};

const ReportAssistant: React.FC<Props> = observer((props) => {
	const [state] = useState(() => new State());
	const { reportResponse } = props;

	const userContext = useUserContext();
	const { height } = useWindowSize();

	const getEfficiencyReportSummary = TrpcReact.getEfficiencyReportSummary2.useQuery(
		{
			report: reportResponse,
			idx: state.viewState.completionIdx,
		},
		{ retry: false, enabled: state.enabled },
	);

	const responseData = useMemo(
		() => extractDataFromEffReportAnalysisArray(getEfficiencyReportSummary.data ?? []),
		[getEfficiencyReportSummary.data],
	);

	return (
		<div
			className='antd-reset report-assistant-container' //report-assistant-container was added to increase specificity of css, as it could be overriden by antd-reset.
			css={css`
				&.report-assistant-container {
					z-index: 10;
					position: fixed;
					right: 20px; //change to 0 after CF support-chatbot branch is released
					bottom: 75px; //change to 70px after CF support-chatbot branch is released
					display: flex;
					flex-direction: column;
					align-items: flex-end;
				}
			`}
		>
			{state.isVisible && (
				<Card
					title={
						<div
							css={css`
								display: flex;
								flex-direction: row;
								gap: 10px;
							`}
						>
							<b
								css={css`
									flex: 1;
								`}
							>
								Virtual Farm Assistant
							</b>
							{responseData &&
								responseData.responseId &&
								(state.editingFeedback ? (
									<Button key='close' type='primary' size='small' onClick={() => state.stopFeedback()}>
										Close feedback
									</Button>
								) : (
									<Button
										key='feedback'
										icon='notification'
										type='primary'
										size='small'
										onClick={() =>
											state.editFeedback(
												new FeedbackState(
													responseData.response!,
													responseData.causeAnalyses!.map((a) => a.fullAnalysis),
													responseData.responseId!,
												),
											)
										}
									>
										Give feedback
									</Button>
								))}
							{userContext.role === 'backoffice' && (
								<Checkbox checked={state.toolsVisible} onChange={(e) => state.setToolsVisible(e.target.checked)}>
									Show tools
								</Checkbox>
							)}
						</div>
					}
					size='small'
					css={css`
						width: 800px;
						box-shadow:
							0 4px 8px rgba(0, 0, 0, 0.1),
							0 6px 20px rgba(0, 0, 0, 0.1);
					`}
				>
					<div
						css={css`
							height: ${height - 280}px;
							overflow: auto;
							display: flex;
							flex-direction: column;
							gap: 1rem;
							padding-right: 10px;
						`}
					>
						{responseData.responseId && state.feedbackStates[responseData.responseId] !== undefined && state.editingFeedback ? (
							<FeedbackMode
								state={state.feedbackStates[responseData.responseId]}
								goBack={() => state.stopFeedback()}
								analysisOpenedStates={state.openedAnalysis}
							/>
						) : (
							<EffReportSummaryView
								data={responseData}
								state={state.viewState}
								internalToolsVisible={state.toolsVisible}
								editFeedback={(feedbackState) => state.editFeedback(feedbackState)}
								analysisOpenedStates={state.openedAnalysis}
							/>
						)}
						{getEfficiencyReportSummary.error && <LoadingError error={getEfficiencyReportSummary.error} />}
					</div>
				</Card>
			)}

			<Button
				title='Virtual Farm Assistant'
				type='primary'
				icon='robot'
				shape='circle'
				size='large'
				css={css`
					margin: 10px !important;
				`}
				onClick={() => state.toggleVisibility()}
			/>
		</div>
	);
});

export const ReactComponent = (props: Props) => {
	return (
		<Providers jwtToken={props.jwtToken} languageCode={props.reportResponse.language}>
			<ReportAssistant {...props} />
		</Providers>
	);
};

export function showAssistant(targetElement: HTMLElement, jwtToken: string, reportResponse: EfficiencyReportData) {
	createRoot(targetElement).render(<ReactComponent reportResponse={reportResponse} jwtToken={jwtToken} />);
}
