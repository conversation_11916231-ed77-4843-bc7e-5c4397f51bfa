/** @jsxImportSource @emotion/react */
import React, { useState } from 'react';
import { match, P } from 'ts-pattern';
import { Pending } from '@src/components/Pending';
import shadow from 'react-shadow';
import PigbotMarkdown from '@src/components/common/PigbotMarkdown';
import Timeline from 'antd/lib/timeline/Timeline';
import 'antd/lib/timeline/style/index.less';
import { UsageStatistics } from '@src/components/common/UsageStatistics';
import { css } from '@emotion/react';
import { EffReportSummaryStep, EffReportSummaryStepValues } from 'pigbot-core/src/eff-report/EffReportSummaryResponseChunk';
import Button from 'antd/lib/button';
import { FeedbackState } from '@src/components/report_assistant/FeedbackMode';
import { AnalysisComponent } from '@src/components/report_assistant/AnalysisComponent';
import Alert from 'antd/lib/alert';
import { AnalysisOpenedStates } from '@src/components/report_assistant/ReportAssistant';
import 'antd/lib/popover/style/index.less';
import Popover from 'antd/lib/popover';
import Checkbox from 'antd/lib/checkbox';
import { observer } from 'mobx-react-lite';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import FrontendConfig from '@src/common/FrontendConfig';
import { FixedIcon } from '@src/components/common/FixedIcon';
import { splitByHorizontalRule } from 'pigbot-core/src/utils/markdownUtils';
import { EffReportSummaryViewerState } from '@src/components/report_assistant/EffReportSummaryViewerState';
import { useUserContext } from '@src/components/Providers';

/**
 * Converts step to text
 */
function step2Text(step: EffReportSummaryStep): string {
	switch (step) {
		case 'IdentifyingIssues':
			return 'Identifying issues';
		case 'Summarizing':
			return 'Preparing summary';
		case 'getRelevantData':
			return 'Collecting additional data';
		case 'AnalyzeCauses':
			return 'Analyzing';
	}
}

type EffReportSummaryViewData = ReturnType<typeof extractDataFromEffReportAnalysisArray>;

/**
 * Render info text in an unobtrusive way.
 */
function SimpleInfo({ children }: { children: string }) {
	return (
		<div
			css={css`
				padding: 10px;
			`}
		>
			<FixedIcon type='info-circle' theme='twoTone' /> {children}
		</div>
	);
}

/**
 * Renders eff report summary response stream with a timeline to show the progress
 */
export const EffReportSummaryView: React.FC<{
	data: EffReportSummaryViewData;
	state: EffReportSummaryViewerState;
	internalToolsVisible: boolean;
	editFeedback: ((feedbackState: FeedbackState) => void) | null;
	analysisOpenedStates: AnalysisOpenedStates;
}> = observer((props) => {
	// Extract data from the stream
	const data = props.data;
	const responsesSplit = data.response ? splitByHorizontalRule(data.response) : undefined;
	const responseStreamComplete = data.responseId != null;

	const userContext = useUserContext();

	const timelinePageLink = <a href='/#/farmTimeline'>here</a>;

	const farmTimelineInfo = match(data.farmTimeline)
		.with(Pending, () => null)
		.with(P.array(), (farmTimeline) => {
			const nonEmptyTimeline = farmTimeline.length > 0;

			return (
				<Alert
					message={
						nonEmptyTimeline ? (
							<shadow.div>
								<strong>Farm timeline</strong>
								<ul>
									{farmTimeline.map((entry) => (
										<li key={entry.description}>
											<strong>
												{entry.to ?? 'Ongoing'} - {entry.from}
											</strong>
											: {entry.description}
										</li>
									))}
								</ul>
								The timeline can be updated {timelinePageLink}.
							</shadow.div>
						) : (
							<span>
								This farm has no timeline records. The timeline can be used to provide the assistant with more context about the farm, such
								as disease outbreaks, labor shortages, and other important information. The timeline can be updated {timelinePageLink}.
							</span>
						)
					}
				/>
			);
		})
		.exhaustive();

	const [showConflictingGoals, setShowConflictingGoals] = useState(false);

	const dataIssuesWarning = data.conflictingGoals && (
		<Alert
			type='warning'
			closable={true}
			message={
				<>
					<p>
						<strong>Conflicting goals</strong>
					</p>
					{showConflictingGoals ? (
						<>
							<PigbotMarkdown>{data.conflictingGoals.conflictingGoals.map((str) => `- ${str}`).join('\n')}</PigbotMarkdown>
							<a
								href={`hide-goals-${data.conflictingGoals.hash}`}
								onClick={(e) => {
									e.preventDefault();
									setShowConflictingGoals(false);
								}}
							>
								Hide ▲
							</a>
						</>
					) : (
						<>
							{data.conflictingGoals.conflictingGoals.length} potentially conflicting goals detected.{' '}
							<a
								href={`show-goals-${data.conflictingGoals.hash}`}
								onClick={(e) => {
									e.preventDefault();
									setShowConflictingGoals(true);
								}}
							>
								Show ▼
							</a>
						</>
					)}
				</>
			}
		/>
	);

	// (data as any).response = null; // Using this to test loading UI

	const downloadPdfButtons = (size: 'small' | 'default') => (
		<>
			<Button
				disabled={!responseStreamComplete}
				icon='download'
				size={size}
				type={size === 'default' ? 'primary' : 'dashed'}
				onClick={() => {
					const url = `${FrontendConfig.BACKEND_BASE_URL}/efficiency-report-pdf/${data.responseId}`;
					window.open(url, '_blank');
				}}
			>
				Download PDF
			</Button>

			<Popover
				title='Include in PDF'
				content={
					<div
						css={css`
							display: flex;
							flex-direction: column;
							gap: 1rem;
						`}
					>
						<div>
							<Checkbox checked={!props.state.hiddenSections.has('summary')} onChange={() => props.state.toggleSection('summary')}>
								Summary
							</Checkbox>
							<hr />
							{data.issues?.issues.map((issue, index) => {
								const sectionId = `issue-${index}`;
								return (
									<div key={index}>
										<Checkbox checked={!props.state.hiddenSections.has(sectionId)} onChange={() => props.state.toggleSection(sectionId)}>
											{String.fromCharCode(65 + index) + '. ' + issue.title}
										</Checkbox>
									</div>
								);
							})}
							<hr />
							<Checkbox checked={!props.state.hiddenSections.has('analyses')} onChange={() => props.state.toggleSection('analyses')}>
								In-depth analyses
							</Checkbox>
						</div>

						<Button
							type='primary'
							onClick={() => {
								const url = `${FrontendConfig.BACKEND_BASE_URL}/efficiency-report-pdf/${data.responseId}?hiddenSections=${Array.from(
									props.state.hiddenSections,
								).join(',')}`;
								window.open(url, '_blank');
							}}
						>
							Download
						</Button>
					</div>
				}
				trigger='click'
			>
				<Button icon='setting' size={size} type={size === 'default' ? 'default' : 'dashed'} disabled={!responseStreamComplete}>
					Custom PDF
				</Button>
			</Popover>
		</>
	);
	const ButtonRowCss = css`
		display: flex;
		gap: 1rem;
		flex-direction: row;
	`;
	const content =
		data.response && data.response.length > 100 ? (
			<>
				{responsesSplit?.map((response, index) => {
					const isFullyRendered = index < responsesSplit.length - 1 || responseStreamComplete;

					return (
						<div key={index} id={`issue-${data.responseId}-${index}`}>
							<PigbotMarkdown>{response}</PigbotMarkdown>
							{isFullyRendered && data.causeAnalyses?.[index] && (
								<AnalysisComponent
									analysisComponent={<PigbotMarkdown>{data.causeAnalyses[index].fullAnalysis}</PigbotMarkdown>}
									isOpened={() => props.analysisOpenedStates.isAnalysisOpen(index)}
									toggleOpened={() => props.analysisOpenedStates.toggleAnalysis(index)}
								/>
							)}
							{isFullyRendered && <hr />}
						</div>
					);
				})}
				{data.responseId && data.farmTimeline?.length == 0 && (
					<Alert
						closable
						message={
							<div>
								If you notice inaccuracies in the analysis that could be addressed by providing additional farm context (e.g., disease
								outbreaks or labor shortages), please visit the Farm Timeline page {timelinePageLink} to add relevant information. This
								helps improve the accuracy of Virtual Farm Assistant.
							</div>
						}
					/>
				)}
				{data.responseId && (
					<div css={ButtonRowCss}>
						{props.editFeedback !== null && (
							<Button
								icon='notification'
								type='primary'
								onClick={() =>
									props.editFeedback!(
										new FeedbackState(
											data.response!,
											data.causeAnalyses!.map((a) => a.fullAnalysis),
											data.responseId!,
										),
									)
								}
							>
								Give feedback
							</Button>
						)}

						{downloadPdfButtons('default')}

						{userContext.role === 'backoffice' && (
							<Button icon='reload' onClick={() => props.state.regenerate()}>
								Regenerate
							</Button>
						)}

						<div
							css={css`
								flex: 1;
							`}
						></div>

						{props.state.completionMax > 0 ? (
							<span>
								{props.state.completionIdx + 1}/{props.state.completionMax + 1}
							</span>
						) : null}
					</div>
				)}

				{props.internalToolsVisible && data.usage && <UsageStatistics usage={data.usage} />}
			</>
		) : (
			<>
				<Timeline
					pending={`${step2Text(data.currentStep)} ...`}
					css={css`
						padding: 10px;
					`}
				>
					{EffReportSummaryStepValues.filter((step, stepIdx) => stepIdx < EffReportSummaryStepValues.indexOf(data.currentStep)).map(
						(step) => (
							<Timeline.Item key={step}>{step2Text(step)}</Timeline.Item>
						),
					)}
				</Timeline>
				<SimpleInfo>Complete analysis of the report takes around 5 minutes.</SimpleInfo>
			</>
		);

	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				gap: 10px;
			`}
		>
			{farmTimelineInfo}
			{dataIssuesWarning}
			{data.issues && (
				<shadow.div>
					<h3>Summary{!data.causeAnalyses && ' (WIP)'}</h3>
					<ul>
						{data.issues.issues.map((issue, index) => {
							const sectionId = `issue-${data.responseId}-${index}`;
							const summary =
								data.causeAnalyses && data.causeAnalyses[index]?.singleSentenceSummary
									? `${issue.summary} ${data.causeAnalyses[index].singleSentenceSummary}`
									: `${issue.summary} ...`;

							return (
								<li key={index}>
									<strong>{String.fromCharCode(65 + index) + '. ' + issue.title}</strong>: {summary}{' '}
									{responseStreamComplete && (
										<a
											href={`#${sectionId}`}
											onClick={(e) => {
												// Prevent default anchor click behavior because it doesn't work correctly in pigman webapp
												// and replace it with JavaScript implementation
												e.preventDefault();
												const element = document.getElementById(sectionId);
												element?.scrollIntoView({ behavior: 'smooth' });
											}}
										>
											Details ↓
										</a>
									)}
								</li>
							);
						})}
					</ul>
				</shadow.div>
			)}
			{data.response && <div css={ButtonRowCss}>{downloadPdfButtons('small')}</div>}
			<shadow.div>
				<hr />
			</shadow.div>
			{content}
		</div>
	);
});
